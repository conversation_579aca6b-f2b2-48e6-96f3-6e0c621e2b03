使用go写一个请求qwen-vl-plus的业务。

DashScope模式请求；请求地址为；https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation

请求体为；
{
  "input": {
    "messages": [
      {
        "content": [
          {
            "text": "精准识别考试题目,严格标准返回json格式。示例{\"type\":\"问题类型\",\"num\":\"序号\",\"text\":\"问题内容\",\"options\":{\"选项标识\":\"选项内容\"}}"
          }
        ],
        "role": "system"
      },
      {
        "content": [
          {
            "image": "http://img.igmdns.com/img/ca0159.jpg"
          },
          {
            "text": "精准识别考试题目。"
          }
        ],
        "role": "user"
      }
    ]
  },
  "model": "qwen-vl-plus",
  "parameters": {
    "presence_penalty": 1.5,
    "repetition_penalty": 1.05,
    "response_format": {
      "type": "json_object"
    },
    "temperature": 0,
    "top_k": 1,
    "top_p": 0.01
  }
}



要求1.记录请求qwen的原始数据。
要求2.记录qwen的原始响应数据。
要求3.需要一个多线程脚本，将提交参数中的图片，http://img.igmdns.com/img/ca0001.jpg请求到http://img.igmdns.com/img/ca1000.jpg；
要求4，将响应结果中的content-text-text中的值打印到一个单独的文件，每次请求结果为一行保存。


qwen的key为sk-3920274bedf642c2b7495f534aadca84