package main

const (
	// API配置
	QwenAPIURL = "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"
	APIKey     = "sk-3920274bedf642c2b7495f534aadca84"
	
	// 图片配置
	ImageBaseURL = "http://img.igmdns.com/img/"
	StartImageNum = 1
	EndImageNum   = 1000
	
	// 文件配置
	RequestLogFile  = "request_logs.json"
	ResponseLogFile = "response_logs.json"
	ResultFile      = "results.txt"
	TextOnlyFile    = "text_only.txt"
	
	// 并发配置
	MaxConcurrency = 10
)

// QwenRequest 请求结构体
type QwenRequest struct {
	Input      Input      `json:"input"`
	Model      string     `json:"model"`
	Parameters Parameters `json:"parameters"`
}

type Input struct {
	Messages []Message `json:"messages"`
}

type Message struct {
	Content []Content `json:"content"`
	Role    string    `json:"role"`
}

type Content struct {
	Text  string `json:"text,omitempty"`
	Image string `json:"image,omitempty"`
}

type Parameters struct {
	PresencePenalty   float64        `json:"presence_penalty"`
	RepetitionPenalty float64        `json:"repetition_penalty"`
	ResponseFormat    ResponseFormat `json:"response_format"`
	Temperature       float64        `json:"temperature"`
	TopK              int            `json:"top_k"`
	TopP              float64        `json:"top_p"`
}

type ResponseFormat struct {
	Type string `json:"type"`
}

// QwenResponse 响应结构体
type QwenResponse struct {
	Output struct {
		Choices []struct {
			Message struct {
				Content []struct {
					Text string `json:"text"`
				} `json:"content"`
			} `json:"message"`
		} `json:"choices"`
	} `json:"output"`
	Usage struct {
		InputTokens  int `json:"input_tokens"`
		OutputTokens int `json:"output_tokens"`
		TotalTokens  int `json:"total_tokens"`
	} `json:"usage"`
	RequestID string `json:"request_id"`
}
