package main

import (
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"time"
)

type Logger struct {
	requestFile  *os.File
	responseFile *os.File
	resultFile   *os.File
	textOnlyFile *os.File
	mutex        sync.Mutex
}

type LogEntry struct {
	Timestamp string      `json:"timestamp"`
	ImageNum  int         `json:"image_num"`
	Data      interface{} `json:"data"`
}

func NewLogger() (*Logger, error) {
	requestFile, err := os.OpenFile(RequestLogFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, fmt.Errorf("failed to open request log file: %v", err)
	}

	responseFile, err := os.OpenFile(ResponseLogFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		requestFile.Close()
		return nil, fmt.Errorf("failed to open response log file: %v", err)
	}

	resultFile, err := os.OpenFile(ResultFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		requestFile.Close()
		responseFile.Close()
		return nil, fmt.Errorf("failed to open result file: %v", err)
	}

	textOnlyFile, err := os.OpenFile(TextOnlyFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		requestFile.Close()
		responseFile.Close()
		resultFile.Close()
		return nil, fmt.Errorf("failed to open text only file: %v", err)
	}

	return &Logger{
		requestFile:  requestFile,
		responseFile: responseFile,
		resultFile:   resultFile,
		textOnlyFile: textOnlyFile,
	}, nil
}

func (l *Logger) LogRequest(imageNum int, request *QwenRequest) error {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	entry := LogEntry{
		Timestamp: time.Now().Format(time.RFC3339),
		ImageNum:  imageNum,
		Data:      request,
	}

	data, err := json.Marshal(entry)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %v", err)
	}

	_, err = l.requestFile.WriteString(string(data) + "\n")
	if err != nil {
		return fmt.Errorf("failed to write request log: %v", err)
	}

	return l.requestFile.Sync()
}

func (l *Logger) LogResponse(imageNum int, response *QwenResponse) error {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	entry := LogEntry{
		Timestamp: time.Now().Format(time.RFC3339),
		ImageNum:  imageNum,
		Data:      response,
	}

	data, err := json.Marshal(entry)
	if err != nil {
		return fmt.Errorf("failed to marshal response: %v", err)
	}

	_, err = l.responseFile.WriteString(string(data) + "\n")
	if err != nil {
		return fmt.Errorf("failed to write response log: %v", err)
	}

	return l.responseFile.Sync()
}

func (l *Logger) LogResult(imageNum int, result string) error {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	line := fmt.Sprintf("[%s] Image %04d: %s\n", 
		time.Now().Format(time.RFC3339), imageNum, result)
	
	_, err := l.resultFile.WriteString(line)
	if err != nil {
		return fmt.Errorf("failed to write result: %v", err)
	}

	return l.resultFile.Sync()
}

func (l *Logger) LogTextOnly(imageNum int, text string) error {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	line := fmt.Sprintf("[%s] Image %04d: %s\n",
		time.Now().Format(time.RFC3339), imageNum, text)

	_, err := l.textOnlyFile.WriteString(line)
	if err != nil {
		return fmt.Errorf("failed to write text only log: %v", err)
	}

	return l.textOnlyFile.Sync()
}

func (l *Logger) Close() {
	l.requestFile.Close()
	l.responseFile.Close()
	l.resultFile.Close()
	l.textOnlyFile.Close()
}
