# Qwen-VL-Plus 图片处理程序

这是一个使用 Go 语言开发的程序，用于批量调用阿里云 DashScope 的 qwen-vl-plus 模型来识别考试题目。

## 功能特性

1. **批量处理图片**：自动处理从 ca0001.jpg 到 ca1000.jpg 的图片
2. **多线程并发**：支持多个 goroutine 并发处理，提高效率
3. **完整日志记录**：记录所有请求和响应的原始数据
4. **结果提取**：自动提取并保存识别结果
5. **错误处理**：完善的错误处理和重试机制

## 文件结构

```
├── main.go           # 主程序入口
├── config.go         # 配置和数据结构定义
├── qwen_client.go    # Qwen API 客户端
├── logger.go         # 日志记录功能
├── go.mod           # Go 模块文件
└── README.md        # 说明文档
```

## 配置说明

程序中的主要配置项（在 `config.go` 中）：

- `QwenAPIURL`: DashScope API 地址
- `APIKey`: 您的 API 密钥
- `ImageBaseURL`: 图片的基础 URL
- `StartImageNum`: 开始处理的图片序号（默认：1）
- `EndImageNum`: 结束处理的图片序号（默认：1000）
- `MaxConcurrency`: 最大并发数（默认：10）

## 输出文件

程序运行后会生成以下文件：

1. **request_logs.json**: 记录所有发送给 Qwen 的请求数据
2. **response_logs.json**: 记录所有从 Qwen 收到的响应数据
3. **results.txt**: 提取的识别结果，每行一个完整的JSON结果
4. **text_only.txt**: 仅包含题目文本内容，方便测试和分析

## 使用方法

### 1. 编译程序

```bash
go build -o qwen-processor
```

### 2. 测试运行（推荐）

首先运行测试模式，只处理前3张图片：

```bash
./qwen-processor test
```

### 3. 正式运行

确认测试无误后，运行完整程序：

```bash
./qwen-processor
```

## 请求格式

程序会为每张图片构建如下格式的请求：

```json
{
  "input": {
    "messages": [
      {
        "content": [
          {
            "text": "精准识别考试题目,严格标准返回json格式。示例{\"type\":\"问题类型\",\"num\":\"序号\",\"text\":\"问题内容\",\"options\":{\"选项标识\":\"选项内容\"}}"
          }
        ],
        "role": "system"
      },
      {
        "content": [
          {
            "image": "http://img.igmdns.com/img/ca0001.jpg"
          },
          {
            "text": "精准识别考试题目。"
          }
        ],
        "role": "user"
      }
    ]
  },
  "model": "qwen-vl-plus",
  "parameters": {
    "presence_penalty": 1.5,
    "repetition_penalty": 1.05,
    "response_format": {
      "type": "json_object"
    },
    "temperature": 0,
    "top_k": 1,
    "top_p": 0.01
  }
}
```

## 注意事项

1. **API 密钥安全**：请确保您的 API 密钥安全，不要泄露给他人
2. **网络连接**：程序需要稳定的网络连接来访问 DashScope API
3. **并发限制**：建议根据您的 API 配额调整 `MaxConcurrency` 参数
4. **错误处理**：程序会自动重试失败的请求，但请注意查看日志中的错误信息
5. **存储空间**：确保有足够的磁盘空间存储日志文件

## 故障排除

如果遇到问题，请检查：

1. API 密钥是否正确
2. 网络连接是否正常
3. 图片 URL 是否可访问
4. API 配额是否充足

## 性能优化

- 可以根据网络状况和 API 限制调整 `MaxConcurrency` 参数
- 可以修改请求间隔时间来避免触发 API 限流
- 可以实现断点续传功能，避免重复处理已完成的图片
