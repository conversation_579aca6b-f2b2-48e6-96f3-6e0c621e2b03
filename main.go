package main

import (
	"fmt"
	"log"
	"os"
	"sync"
	"time"
)

// 测试版本的配置
const (
	TestStartImageNum = 1
	TestEndImageNum   = 3  // 只测试前3张图片
	TestMaxConcurrency = 2
)

func testMain() {
	fmt.Println("Starting Qwen-VL-Plus Image Processing Test...")
	fmt.Printf("Testing images from ca%04d.jpg to ca%04d.jpg\n", TestStartImageNum, TestEndImageNum)
	fmt.Printf("Max concurrency: %d\n", TestMaxConcurrency)

	// 初始化日志记录器
	logger, err := NewLogger()
	if err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	defer logger.Close()

	// 初始化Qwen客户端
	client := NewQwenClient(logger)

	// 创建工作队列和结果通道
	imageQueue := make(chan int, TestMaxConcurrency*2)
	var wg sync.WaitGroup

	// 启动工作协程
	for i := 0; i < TestMaxConcurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			fmt.Printf("Worker %d started\n", workerID)

			for imageNum := range imageQueue {
				if err := client.ProcessImage(imageNum); err != nil {
					log.Printf("Worker %d failed to process image %04d: %v", workerID, imageNum, err)
				}
				// 添加小延迟避免请求过于频繁
				time.Sleep(500 * time.Millisecond)
			}

			fmt.Printf("Worker %d finished\n", workerID)
		}(i)
	}

	// 发送任务到队列
	go func() {
		defer close(imageQueue)
		for i := TestStartImageNum; i <= TestEndImageNum; i++ {
			imageQueue <- i
		}
	}()

	// 等待所有任务完成
	startTime := time.Now()
	wg.Wait()
	duration := time.Since(startTime)

	fmt.Printf("\nTest completed!\n")
	fmt.Printf("Total images processed: %d\n", TestEndImageNum-TestStartImageNum+1)
	fmt.Printf("Total time: %v\n", duration)
	fmt.Printf("Average time per image: %v\n", duration/time.Duration(TestEndImageNum-TestStartImageNum+1))

	fmt.Printf("\nOutput files:\n")
	fmt.Printf("- Request logs: %s\n", RequestLogFile)
	fmt.Printf("- Response logs: %s\n", ResponseLogFile)
	fmt.Printf("- Results: %s\n", ResultFile)
	fmt.Printf("- Text only: %s\n", TextOnlyFile)
}

func main() {
	// 检查是否是测试模式
	if len(os.Args) > 1 && os.Args[1] == "test" {
		testMain()
		return
	}

	fmt.Println("Starting Qwen-VL-Plus Image Processing...")
	fmt.Printf("Processing images from ca%04d.jpg to ca%04d.jpg\n", StartImageNum, EndImageNum)
	fmt.Printf("Max concurrency: %d\n", MaxConcurrency)
	
	// 初始化日志记录器
	logger, err := NewLogger()
	if err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	defer logger.Close()

	// 初始化Qwen客户端
	client := NewQwenClient(logger)

	// 创建工作队列和结果通道
	imageQueue := make(chan int, MaxConcurrency*2)
	var wg sync.WaitGroup

	// 启动工作协程
	for i := 0; i < MaxConcurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			fmt.Printf("Worker %d started\n", workerID)
			
			for imageNum := range imageQueue {
				if err := client.ProcessImage(imageNum); err != nil {
					log.Printf("Worker %d failed to process image %04d: %v", workerID, imageNum, err)
				}
				// 添加小延迟避免请求过于频繁
				time.Sleep(100 * time.Millisecond)
			}
			
			fmt.Printf("Worker %d finished\n", workerID)
		}(i)
	}

	// 发送任务到队列
	go func() {
		defer close(imageQueue)
		for i := StartImageNum; i <= EndImageNum; i++ {
			imageQueue <- i
		}
	}()

	// 等待所有任务完成
	startTime := time.Now()
	wg.Wait()
	duration := time.Since(startTime)

	fmt.Printf("\nProcessing completed!\n")
	fmt.Printf("Total images processed: %d\n", EndImageNum-StartImageNum+1)
	fmt.Printf("Total time: %v\n", duration)
	fmt.Printf("Average time per image: %v\n", duration/time.Duration(EndImageNum-StartImageNum+1))
	
	fmt.Printf("\nOutput files:\n")
	fmt.Printf("- Request logs: %s\n", RequestLogFile)
	fmt.Printf("- Response logs: %s\n", ResponseLogFile)
	fmt.Printf("- Results: %s\n", ResultFile)
	fmt.Printf("- Text only: %s\n", TextOnlyFile)
}
