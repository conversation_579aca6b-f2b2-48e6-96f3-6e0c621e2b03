{"timestamp":"2025-06-13T13:00:57+08:00","image_num":2,"data":{"input":{"messages":[{"content":[{"text":"精准识别考试题目,严格标准返回json格式。示例{\"type\":\"问题类型\",\"num\":\"序号\",\"text\":\"问题内容\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0002.jpg"},{"text":"精准识别考试题目。"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1.5,"repetition_penalty":1.05,"response_format":{"type":"json_object"},"temperature":0,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T13:00:57+08:00","image_num":1,"data":{"input":{"messages":[{"content":[{"text":"精准识别考试题目,严格标准返回json格式。示例{\"type\":\"问题类型\",\"num\":\"序号\",\"text\":\"问题内容\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0001.jpg"},{"text":"精准识别考试题目。"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1.5,"repetition_penalty":1.05,"response_format":{"type":"json_object"},"temperature":0,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T13:00:59+08:00","image_num":3,"data":{"input":{"messages":[{"content":[{"text":"精准识别考试题目,严格标准返回json格式。示例{\"type\":\"问题类型\",\"num\":\"序号\",\"text\":\"问题内容\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0003.jpg"},{"text":"精准识别考试题目。"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1.5,"repetition_penalty":1.05,"response_format":{"type":"json_object"},"temperature":0,"top_k":1,"top_p":0.01}}}
