{"timestamp":"2025-06-13T14:02:49+08:00","image_num":2,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0002.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:49+08:00","image_num":3,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0003.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:49+08:00","image_num":4,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0004.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:49+08:00","image_num":5,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0005.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:49+08:00","image_num":6,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0006.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:49+08:00","image_num":7,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0007.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:49+08:00","image_num":1,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0001.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:49+08:00","image_num":8,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0008.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:49+08:00","image_num":9,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0009.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:49+08:00","image_num":10,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0010.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:51+08:00","image_num":11,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0011.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:51+08:00","image_num":12,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0012.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:51+08:00","image_num":13,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0013.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:51+08:00","image_num":14,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0014.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:51+08:00","image_num":15,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0015.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:51+08:00","image_num":16,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0016.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:51+08:00","image_num":17,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0017.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:51+08:00","image_num":18,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0018.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:52+08:00","image_num":19,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0019.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:52+08:00","image_num":20,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0020.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:53+08:00","image_num":21,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0021.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:53+08:00","image_num":22,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0022.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:53+08:00","image_num":23,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0023.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:53+08:00","image_num":24,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0024.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:53+08:00","image_num":25,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0025.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:53+08:00","image_num":26,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0026.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:53+08:00","image_num":27,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0027.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:54+08:00","image_num":28,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0028.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:54+08:00","image_num":29,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0029.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:55+08:00","image_num":30,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0030.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:55+08:00","image_num":31,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0031.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:55+08:00","image_num":32,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0032.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:55+08:00","image_num":33,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0033.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:55+08:00","image_num":34,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0034.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:56+08:00","image_num":35,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0035.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:56+08:00","image_num":36,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0036.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:56+08:00","image_num":37,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0037.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:56+08:00","image_num":38,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0038.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:57+08:00","image_num":39,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0039.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:57+08:00","image_num":40,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0040.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:57+08:00","image_num":41,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0041.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:57+08:00","image_num":42,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0042.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:58+08:00","image_num":43,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0043.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:58+08:00","image_num":44,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0044.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:58+08:00","image_num":45,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0045.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:58+08:00","image_num":46,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0046.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:58+08:00","image_num":47,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0047.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:58+08:00","image_num":48,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0048.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:58+08:00","image_num":49,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0049.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:59+08:00","image_num":50,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0050.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:02:59+08:00","image_num":51,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0051.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:00+08:00","image_num":52,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0052.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:00+08:00","image_num":53,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0053.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:00+08:00","image_num":54,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0054.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:00+08:00","image_num":55,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0055.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:00+08:00","image_num":56,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0056.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:00+08:00","image_num":57,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0057.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:00+08:00","image_num":58,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0058.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:00+08:00","image_num":59,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0059.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:01+08:00","image_num":60,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0060.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:02+08:00","image_num":61,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0061.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:02+08:00","image_num":62,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0062.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:02+08:00","image_num":63,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0063.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:02+08:00","image_num":64,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0064.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:02+08:00","image_num":65,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0065.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:02+08:00","image_num":66,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0066.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:02+08:00","image_num":67,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0067.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:03+08:00","image_num":68,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0068.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:03+08:00","image_num":69,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0069.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:03+08:00","image_num":70,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0070.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:03+08:00","image_num":71,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0071.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:04+08:00","image_num":72,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0072.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:04+08:00","image_num":73,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0073.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:04+08:00","image_num":74,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0074.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:04+08:00","image_num":75,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0075.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:05+08:00","image_num":76,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0076.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:05+08:00","image_num":77,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0077.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:05+08:00","image_num":78,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0078.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:06+08:00","image_num":79,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0079.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:06+08:00","image_num":80,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0080.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:06+08:00","image_num":81,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0081.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:06+08:00","image_num":82,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0082.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:06+08:00","image_num":83,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0083.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:06+08:00","image_num":84,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0084.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:07+08:00","image_num":85,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0085.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:07+08:00","image_num":86,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0086.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:07+08:00","image_num":87,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0087.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:07+08:00","image_num":88,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0088.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:07+08:00","image_num":89,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0089.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:08+08:00","image_num":90,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0090.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:08+08:00","image_num":91,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0091.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:08+08:00","image_num":92,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0092.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:08+08:00","image_num":93,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0093.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:09+08:00","image_num":94,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0094.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:09+08:00","image_num":95,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0095.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:09+08:00","image_num":96,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0096.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:09+08:00","image_num":97,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0097.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:09+08:00","image_num":98,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0098.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:09+08:00","image_num":99,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0099.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:10+08:00","image_num":100,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0100.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:10+08:00","image_num":101,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0101.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:10+08:00","image_num":102,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0102.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:11+08:00","image_num":103,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0103.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:11+08:00","image_num":104,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0104.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:11+08:00","image_num":105,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0105.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:11+08:00","image_num":106,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0106.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:11+08:00","image_num":107,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0107.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:12+08:00","image_num":108,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0108.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:12+08:00","image_num":109,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0109.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:12+08:00","image_num":110,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0110.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:12+08:00","image_num":111,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0111.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:12+08:00","image_num":112,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0112.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:13+08:00","image_num":113,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0113.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:13+08:00","image_num":114,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0114.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:13+08:00","image_num":115,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0115.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:13+08:00","image_num":116,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0116.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:13+08:00","image_num":117,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0117.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:14+08:00","image_num":118,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0118.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:14+08:00","image_num":119,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0119.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:14+08:00","image_num":120,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0120.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:14+08:00","image_num":121,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0121.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:14+08:00","image_num":122,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0122.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:14+08:00","image_num":123,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0123.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:15+08:00","image_num":124,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0124.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:15+08:00","image_num":125,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0125.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:16+08:00","image_num":126,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0126.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:16+08:00","image_num":127,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0127.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:16+08:00","image_num":128,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0128.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:16+08:00","image_num":129,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0129.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:16+08:00","image_num":130,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0130.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:16+08:00","image_num":131,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0131.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:17+08:00","image_num":132,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0132.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:17+08:00","image_num":133,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0133.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:18+08:00","image_num":134,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0134.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:18+08:00","image_num":135,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0135.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:18+08:00","image_num":136,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0136.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:18+08:00","image_num":137,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0137.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:18+08:00","image_num":138,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0138.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:18+08:00","image_num":139,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0139.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:19+08:00","image_num":140,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0140.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:19+08:00","image_num":141,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0141.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:19+08:00","image_num":142,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0142.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:19+08:00","image_num":143,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0143.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:20+08:00","image_num":144,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0144.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:20+08:00","image_num":145,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0145.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:20+08:00","image_num":146,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0146.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:21+08:00","image_num":147,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0147.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:21+08:00","image_num":148,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0148.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:21+08:00","image_num":149,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0149.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:21+08:00","image_num":150,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0150.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:21+08:00","image_num":151,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0151.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:22+08:00","image_num":152,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0152.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:22+08:00","image_num":153,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0153.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:22+08:00","image_num":154,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0154.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:22+08:00","image_num":155,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0155.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:22+08:00","image_num":156,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0156.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:22+08:00","image_num":157,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0157.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:23+08:00","image_num":158,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0158.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:23+08:00","image_num":159,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0159.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:23+08:00","image_num":160,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0160.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:23+08:00","image_num":161,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0161.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:24+08:00","image_num":162,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0162.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:24+08:00","image_num":163,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0163.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:24+08:00","image_num":164,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0164.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:25+08:00","image_num":165,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0165.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:25+08:00","image_num":166,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0166.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:25+08:00","image_num":167,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0167.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:25+08:00","image_num":168,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0168.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:26+08:00","image_num":169,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0169.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:26+08:00","image_num":170,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0170.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:26+08:00","image_num":171,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0171.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:26+08:00","image_num":172,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0172.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:26+08:00","image_num":173,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0173.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:26+08:00","image_num":174,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0174.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:27+08:00","image_num":175,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0175.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:27+08:00","image_num":176,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0176.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:28+08:00","image_num":177,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0177.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:28+08:00","image_num":178,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0178.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:28+08:00","image_num":179,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0179.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:28+08:00","image_num":180,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0180.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:28+08:00","image_num":181,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0181.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:28+08:00","image_num":182,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0182.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:28+08:00","image_num":183,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0183.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:29+08:00","image_num":184,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0184.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:29+08:00","image_num":185,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0185.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:30+08:00","image_num":186,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0186.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:30+08:00","image_num":187,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0187.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:30+08:00","image_num":188,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0188.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:30+08:00","image_num":189,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0189.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:30+08:00","image_num":190,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0190.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:31+08:00","image_num":191,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0191.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:31+08:00","image_num":192,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0192.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:31+08:00","image_num":193,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0193.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:31+08:00","image_num":194,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0194.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:31+08:00","image_num":195,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0195.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:32+08:00","image_num":196,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0196.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:32+08:00","image_num":197,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0197.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:32+08:00","image_num":198,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0198.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:32+08:00","image_num":199,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0199.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:33+08:00","image_num":200,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0200.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:33+08:00","image_num":201,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0201.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:33+08:00","image_num":202,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0202.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:33+08:00","image_num":203,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0203.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:33+08:00","image_num":204,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0204.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:34+08:00","image_num":205,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0205.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:34+08:00","image_num":206,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0206.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:35+08:00","image_num":207,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0207.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:35+08:00","image_num":208,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0208.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:35+08:00","image_num":209,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0209.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:35+08:00","image_num":210,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0210.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:35+08:00","image_num":211,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0211.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:35+08:00","image_num":212,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0212.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:36+08:00","image_num":213,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0213.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:36+08:00","image_num":214,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0214.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:36+08:00","image_num":215,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0215.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:36+08:00","image_num":216,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0216.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:37+08:00","image_num":217,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0217.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:37+08:00","image_num":218,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0218.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:37+08:00","image_num":219,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0219.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:37+08:00","image_num":220,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0220.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:37+08:00","image_num":221,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0221.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:38+08:00","image_num":222,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0222.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:38+08:00","image_num":223,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0223.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:38+08:00","image_num":224,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0224.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:38+08:00","image_num":225,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0225.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:38+08:00","image_num":226,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0226.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:39+08:00","image_num":227,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0227.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:39+08:00","image_num":228,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0228.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:39+08:00","image_num":229,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0229.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:40+08:00","image_num":230,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0230.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:40+08:00","image_num":231,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0231.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:40+08:00","image_num":232,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0232.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:40+08:00","image_num":233,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0233.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:40+08:00","image_num":234,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0234.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:40+08:00","image_num":235,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0235.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:40+08:00","image_num":236,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0236.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:41+08:00","image_num":237,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0237.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:41+08:00","image_num":238,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0238.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:41+08:00","image_num":239,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0239.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:41+08:00","image_num":240,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0240.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:42+08:00","image_num":241,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0241.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:42+08:00","image_num":242,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0242.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:42+08:00","image_num":243,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0243.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:42+08:00","image_num":244,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0244.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:42+08:00","image_num":245,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0245.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:43+08:00","image_num":246,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0246.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:43+08:00","image_num":247,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0247.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:43+08:00","image_num":248,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0248.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:44+08:00","image_num":249,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0249.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:44+08:00","image_num":250,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0250.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:44+08:00","image_num":251,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0251.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:44+08:00","image_num":252,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0252.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:44+08:00","image_num":253,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0253.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:44+08:00","image_num":254,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0254.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:45+08:00","image_num":255,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0255.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:45+08:00","image_num":256,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0256.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:45+08:00","image_num":257,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0257.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:46+08:00","image_num":258,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0258.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:46+08:00","image_num":259,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0259.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:46+08:00","image_num":260,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0260.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:46+08:00","image_num":261,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0261.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:46+08:00","image_num":262,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0262.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:46+08:00","image_num":263,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0263.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:47+08:00","image_num":264,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0264.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:47+08:00","image_num":265,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0265.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:47+08:00","image_num":266,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0266.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:48+08:00","image_num":267,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0267.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:48+08:00","image_num":268,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0268.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:48+08:00","image_num":269,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0269.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:48+08:00","image_num":270,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0270.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:49+08:00","image_num":271,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0271.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:49+08:00","image_num":272,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0272.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:49+08:00","image_num":273,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0273.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:49+08:00","image_num":274,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0274.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:49+08:00","image_num":275,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0275.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:50+08:00","image_num":276,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0276.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:50+08:00","image_num":277,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0277.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:50+08:00","image_num":278,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0278.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:50+08:00","image_num":279,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0279.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:51+08:00","image_num":280,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0280.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:51+08:00","image_num":281,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0281.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:51+08:00","image_num":282,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0282.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:51+08:00","image_num":283,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0283.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:51+08:00","image_num":284,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0284.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:51+08:00","image_num":285,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0285.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:52+08:00","image_num":286,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0286.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:52+08:00","image_num":287,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0287.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:52+08:00","image_num":288,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0288.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:53+08:00","image_num":289,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0289.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:53+08:00","image_num":290,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0290.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:53+08:00","image_num":291,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0291.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:53+08:00","image_num":292,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0292.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:53+08:00","image_num":293,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0293.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:53+08:00","image_num":294,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0294.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:53+08:00","image_num":295,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0295.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:54+08:00","image_num":296,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0296.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:54+08:00","image_num":297,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0297.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:54+08:00","image_num":298,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0298.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:54+08:00","image_num":299,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0299.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:55+08:00","image_num":300,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0300.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:55+08:00","image_num":301,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0301.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:55+08:00","image_num":302,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0302.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:55+08:00","image_num":303,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0303.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:55+08:00","image_num":304,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0304.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:56+08:00","image_num":305,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0305.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:56+08:00","image_num":306,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0306.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:56+08:00","image_num":307,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0307.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:57+08:00","image_num":308,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0308.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:57+08:00","image_num":309,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0309.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:57+08:00","image_num":310,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0310.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:57+08:00","image_num":311,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0311.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:57+08:00","image_num":312,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0312.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:57+08:00","image_num":313,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0313.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:57+08:00","image_num":314,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0314.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:57+08:00","image_num":315,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0315.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:58+08:00","image_num":316,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0316.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:58+08:00","image_num":317,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0317.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:58+08:00","image_num":318,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0318.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:58+08:00","image_num":319,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0319.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:59+08:00","image_num":320,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0320.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:59+08:00","image_num":321,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0321.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:59+08:00","image_num":322,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0322.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:59+08:00","image_num":323,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0323.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:03:59+08:00","image_num":324,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0324.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:00+08:00","image_num":325,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0325.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:00+08:00","image_num":326,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0326.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:00+08:00","image_num":327,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0327.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:00+08:00","image_num":328,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0328.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:00+08:00","image_num":329,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0329.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:00+08:00","image_num":330,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0330.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:00+08:00","image_num":331,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0331.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:01+08:00","image_num":332,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0332.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:01+08:00","image_num":333,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0333.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:01+08:00","image_num":334,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0334.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:02+08:00","image_num":335,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0335.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:02+08:00","image_num":336,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0336.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:02+08:00","image_num":337,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0337.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:02+08:00","image_num":338,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0338.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:02+08:00","image_num":339,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0339.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:03+08:00","image_num":340,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0340.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:03+08:00","image_num":341,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0341.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:03+08:00","image_num":342,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0342.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:03+08:00","image_num":343,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0343.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:03+08:00","image_num":344,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0344.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:04+08:00","image_num":345,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0345.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:04+08:00","image_num":346,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0346.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:04+08:00","image_num":347,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0347.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:04+08:00","image_num":348,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0348.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:05+08:00","image_num":349,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0349.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:05+08:00","image_num":350,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0350.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:05+08:00","image_num":351,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0351.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:05+08:00","image_num":352,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0352.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:06+08:00","image_num":353,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0353.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:06+08:00","image_num":354,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0354.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:06+08:00","image_num":355,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0355.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:06+08:00","image_num":356,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0356.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:06+08:00","image_num":357,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0357.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:07+08:00","image_num":358,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0358.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:07+08:00","image_num":359,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0359.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:07+08:00","image_num":360,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0360.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:07+08:00","image_num":361,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0361.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:07+08:00","image_num":362,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0362.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:08+08:00","image_num":363,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0363.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:08+08:00","image_num":364,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0364.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:08+08:00","image_num":365,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0365.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:08+08:00","image_num":366,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0366.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:09+08:00","image_num":367,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0367.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:09+08:00","image_num":368,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0368.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:09+08:00","image_num":369,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0369.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:09+08:00","image_num":370,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0370.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:09+08:00","image_num":371,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0371.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:10+08:00","image_num":372,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0372.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:10+08:00","image_num":373,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0373.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:10+08:00","image_num":374,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0374.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:10+08:00","image_num":375,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0375.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:10+08:00","image_num":376,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0376.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:11+08:00","image_num":377,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0377.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:11+08:00","image_num":378,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0378.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:11+08:00","image_num":379,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0379.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:11+08:00","image_num":380,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0380.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:11+08:00","image_num":381,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0381.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:12+08:00","image_num":382,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0382.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:12+08:00","image_num":383,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0383.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:12+08:00","image_num":384,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0384.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:12+08:00","image_num":385,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0385.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:12+08:00","image_num":386,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0386.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:13+08:00","image_num":387,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0387.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:13+08:00","image_num":388,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0388.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:13+08:00","image_num":389,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0389.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:13+08:00","image_num":390,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0390.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:13+08:00","image_num":391,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0391.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:14+08:00","image_num":392,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0392.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:14+08:00","image_num":393,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0393.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:14+08:00","image_num":394,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0394.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:14+08:00","image_num":395,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0395.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:14+08:00","image_num":396,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0396.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:14+08:00","image_num":397,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0397.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:15+08:00","image_num":398,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0398.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:15+08:00","image_num":399,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0399.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:15+08:00","image_num":400,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0400.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:15+08:00","image_num":401,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0401.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:15+08:00","image_num":402,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0402.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:16+08:00","image_num":403,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0403.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:16+08:00","image_num":404,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0404.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:16+08:00","image_num":405,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0405.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:16+08:00","image_num":406,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0406.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:16+08:00","image_num":407,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0407.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:17+08:00","image_num":408,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0408.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:17+08:00","image_num":409,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0409.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:17+08:00","image_num":410,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0410.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:17+08:00","image_num":411,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0411.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:18+08:00","image_num":412,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0412.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:18+08:00","image_num":413,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0413.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:18+08:00","image_num":414,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0414.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:18+08:00","image_num":415,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0415.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:18+08:00","image_num":416,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0416.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:19+08:00","image_num":417,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0417.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:19+08:00","image_num":418,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0418.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:19+08:00","image_num":419,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0419.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:20+08:00","image_num":420,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0420.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:20+08:00","image_num":421,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0421.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:20+08:00","image_num":422,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0422.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:20+08:00","image_num":423,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0423.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:20+08:00","image_num":424,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0424.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:20+08:00","image_num":425,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0425.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:20+08:00","image_num":426,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0426.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:21+08:00","image_num":427,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0427.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:21+08:00","image_num":428,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0428.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:21+08:00","image_num":429,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0429.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:22+08:00","image_num":430,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0430.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:22+08:00","image_num":431,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0431.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:22+08:00","image_num":432,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0432.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:22+08:00","image_num":433,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0433.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:22+08:00","image_num":434,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0434.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:22+08:00","image_num":435,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0435.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:22+08:00","image_num":436,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0436.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:23+08:00","image_num":437,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0437.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:23+08:00","image_num":438,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0438.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:23+08:00","image_num":439,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0439.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:24+08:00","image_num":440,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0440.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:24+08:00","image_num":441,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0441.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:24+08:00","image_num":442,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0442.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:24+08:00","image_num":443,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0443.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:24+08:00","image_num":444,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0444.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:24+08:00","image_num":445,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0445.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:24+08:00","image_num":446,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0446.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:25+08:00","image_num":447,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0447.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:25+08:00","image_num":448,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0448.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:25+08:00","image_num":449,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0449.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:26+08:00","image_num":450,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0450.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:26+08:00","image_num":451,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0451.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:26+08:00","image_num":452,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0452.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:26+08:00","image_num":453,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0453.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:26+08:00","image_num":454,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0454.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:27+08:00","image_num":455,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0455.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:27+08:00","image_num":456,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0456.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:27+08:00","image_num":457,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0457.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:27+08:00","image_num":458,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0458.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:27+08:00","image_num":459,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0459.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:28+08:00","image_num":460,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0460.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:28+08:00","image_num":461,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0461.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:28+08:00","image_num":462,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0462.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:28+08:00","image_num":463,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0463.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:29+08:00","image_num":464,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0464.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:29+08:00","image_num":465,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0465.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:29+08:00","image_num":466,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0466.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:29+08:00","image_num":467,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0467.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:29+08:00","image_num":468,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0468.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:29+08:00","image_num":469,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0469.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:29+08:00","image_num":470,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0470.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:29+08:00","image_num":471,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0471.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:30+08:00","image_num":472,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0472.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:30+08:00","image_num":473,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0473.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:30+08:00","image_num":474,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0474.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:30+08:00","image_num":475,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0475.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:31+08:00","image_num":476,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0476.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:31+08:00","image_num":477,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0477.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:31+08:00","image_num":478,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0478.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:31+08:00","image_num":479,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0479.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:31+08:00","image_num":480,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0480.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:31+08:00","image_num":481,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0481.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:32+08:00","image_num":482,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0482.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:32+08:00","image_num":483,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0483.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:32+08:00","image_num":484,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0484.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:32+08:00","image_num":485,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0485.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:33+08:00","image_num":486,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0486.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:33+08:00","image_num":487,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0487.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:33+08:00","image_num":488,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0488.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:33+08:00","image_num":489,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0489.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:33+08:00","image_num":490,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0490.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:33+08:00","image_num":491,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0491.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:34+08:00","image_num":492,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0492.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:34+08:00","image_num":493,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0493.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:34+08:00","image_num":494,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0494.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:35+08:00","image_num":495,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0495.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:35+08:00","image_num":496,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0496.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:35+08:00","image_num":497,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0497.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:35+08:00","image_num":498,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0498.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:35+08:00","image_num":499,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0499.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:35+08:00","image_num":500,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0500.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:36+08:00","image_num":501,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0501.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:36+08:00","image_num":502,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0502.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:36+08:00","image_num":503,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0503.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:36+08:00","image_num":504,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0504.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:37+08:00","image_num":505,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0505.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:37+08:00","image_num":506,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0506.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:37+08:00","image_num":507,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0507.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:37+08:00","image_num":508,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0508.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:37+08:00","image_num":509,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0509.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:37+08:00","image_num":510,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0510.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:37+08:00","image_num":511,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0511.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:38+08:00","image_num":512,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0512.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:38+08:00","image_num":513,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0513.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:38+08:00","image_num":514,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0514.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:39+08:00","image_num":515,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0515.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:39+08:00","image_num":516,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0516.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:39+08:00","image_num":517,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0517.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:39+08:00","image_num":518,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0518.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:39+08:00","image_num":519,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0519.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:39+08:00","image_num":520,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0520.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:39+08:00","image_num":521,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0521.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:40+08:00","image_num":522,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0522.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:40+08:00","image_num":523,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0523.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:41+08:00","image_num":524,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0524.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:41+08:00","image_num":525,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0525.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:41+08:00","image_num":526,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0526.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:41+08:00","image_num":527,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0527.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:41+08:00","image_num":528,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0528.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:41+08:00","image_num":529,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0529.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:41+08:00","image_num":530,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0530.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:42+08:00","image_num":531,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0531.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:42+08:00","image_num":532,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0532.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:42+08:00","image_num":533,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0533.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:43+08:00","image_num":534,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0534.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:43+08:00","image_num":535,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0535.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:43+08:00","image_num":536,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0536.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:43+08:00","image_num":537,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0537.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:43+08:00","image_num":538,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0538.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:43+08:00","image_num":539,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0539.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:43+08:00","image_num":540,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0540.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:44+08:00","image_num":541,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0541.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:44+08:00","image_num":542,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0542.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:44+08:00","image_num":543,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0543.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:45+08:00","image_num":544,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0544.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:45+08:00","image_num":545,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0545.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:45+08:00","image_num":546,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0546.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:45+08:00","image_num":547,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0547.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:45+08:00","image_num":548,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0548.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:45+08:00","image_num":549,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0549.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:46+08:00","image_num":550,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0550.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:46+08:00","image_num":551,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0551.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:46+08:00","image_num":552,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0552.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:46+08:00","image_num":553,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0553.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:47+08:00","image_num":554,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0554.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:47+08:00","image_num":555,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0555.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:47+08:00","image_num":556,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0556.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:47+08:00","image_num":557,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0557.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:47+08:00","image_num":558,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0558.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:48+08:00","image_num":559,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0559.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:48+08:00","image_num":560,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0560.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:48+08:00","image_num":561,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0561.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:48+08:00","image_num":562,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0562.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:49+08:00","image_num":563,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0563.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:49+08:00","image_num":564,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0564.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:49+08:00","image_num":565,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0565.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:49+08:00","image_num":566,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0566.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:49+08:00","image_num":567,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0567.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:49+08:00","image_num":568,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0568.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:49+08:00","image_num":569,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0569.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:49+08:00","image_num":570,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0570.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:50+08:00","image_num":571,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0571.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:50+08:00","image_num":572,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0572.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:51+08:00","image_num":573,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0573.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:51+08:00","image_num":574,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0574.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:51+08:00","image_num":575,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0575.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:51+08:00","image_num":576,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0576.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:51+08:00","image_num":577,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0577.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:52+08:00","image_num":578,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0578.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:52+08:00","image_num":579,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0579.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:52+08:00","image_num":580,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0580.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:52+08:00","image_num":581,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0581.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:04:52+08:00","image_num":582,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"题目类型+序号+正文\",\"options\":{\"选项标识\":\"选项内容\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0582.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
