{"timestamp":"2025-06-13T14:22:07+08:00","image_num":1,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"所有选项\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0001.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:22:07+08:00","image_num":2,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"所有选项\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0002.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:22:07+08:00","image_num":3,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"所有选项\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0003.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:22:07+08:00","image_num":4,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"所有选项\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0004.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:22:07+08:00","image_num":5,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"所有选项\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0005.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:22:07+08:00","image_num":8,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"所有选项\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0008.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:22:07+08:00","image_num":7,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"所有选项\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0007.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:22:07+08:00","image_num":9,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"所有选项\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0009.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:22:07+08:00","image_num":6,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"所有选项\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0006.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
{"timestamp":"2025-06-13T14:22:07+08:00","image_num":10,"data":{"input":{"messages":[{"content":[{"text":"精准识别考题,严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"所有选项\"}}"}],"role":"system"},{"content":[{"image":"http://img.igmdns.com/img/ca0010.jpg"},{"text":"完整且精准的识别"}],"role":"user"}]},"model":"qwen-vl-plus","parameters":{"presence_penalty":1,"repetition_penalty":1,"response_format":{"type":"json_object"},"temperature":0.2,"top_k":1,"top_p":0.01}}}
