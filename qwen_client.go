package main

import (
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"time"
)

type QwenClient struct {
	client *resty.Client
	logger *Logger
}

func NewQwenClient(logger *Logger) *QwenClient {
	client := resty.New()
	client.SetTimeout(30 * time.Second)
	client.SetRetryCount(3)
	client.SetRetryWaitTime(5 * time.Second)
	
	return &QwenClient{
		client: client,
		logger: logger,
	}
}

func (qc *QwenClient) ProcessImage(imageNum int) error {
	// 构建图片URL
	imageURL := fmt.Sprintf("%sca%04d.jpg", ImageBaseURL, imageNum)
	
	// 构建请求体
	request := &QwenRequest{
		Input: Input{
			Messages: []Message{
				{
					Content: []Content{
						{
							Text: "精准识别考题,严格标准返回json格式。示例{\"type\":\"问题类型\",\"num\":\"序号\",\"text\":\"题目正文\",\"options\":{\"选项标识\":\"选项内容\"}}",
						},
					},
					Role: "system",
				},
				{
					Content: []Content{
						{
							Image: imageURL,
						},
						{
							Text: "精准识别考试题目。",
						},
					},
					Role: "user",
				},
			},
		},
		Model: "qwen-vl-plus",
		Parameters: Parameters{
			PresencePenalty:   1.5,
			RepetitionPenalty: 1.05,
			ResponseFormat: ResponseFormat{
				Type: "json_object",
			},
			Temperature: 0.2,
			TopK:        1,
			TopP:        0.01,
		},
	}

	// 记录请求数据
	if err := qc.logger.LogRequest(imageNum, request); err != nil {
		return fmt.Errorf("failed to log request for image %d: %v", imageNum, err)
	}

	// 发送请求
	resp, err := qc.client.R().
		SetHeader("Authorization", "Bearer "+APIKey).
		SetHeader("Content-Type", "application/json").
		SetBody(request).
		Post(QwenAPIURL)

	if err != nil {
		return fmt.Errorf("failed to send request for image %d: %v", imageNum, err)
	}

	if resp.StatusCode() != 200 {
		return fmt.Errorf("API request failed for image %d: status %d, body: %s", 
			imageNum, resp.StatusCode(), string(resp.Body()))
	}

	// 解析响应
	var response QwenResponse
	if err := json.Unmarshal(resp.Body(), &response); err != nil {
		return fmt.Errorf("failed to parse response for image %d: %v", imageNum, err)
	}

	// 记录响应数据
	if err := qc.logger.LogResponse(imageNum, &response); err != nil {
		return fmt.Errorf("failed to log response for image %d: %v", imageNum, err)
	}

	// 提取并记录结果
	if len(response.Output.Choices) > 0 && len(response.Output.Choices[0].Message.Content) > 0 {
		result := response.Output.Choices[0].Message.Content[0].Text
		if err := qc.logger.LogResult(imageNum, result); err != nil {
			return fmt.Errorf("failed to log result for image %d: %v", imageNum, err)
		}
		fmt.Printf("Processed image %04d successfully\n", imageNum)
	} else {
		fmt.Printf("No content found in response for image %04d\n", imageNum)
	}

	return nil
}
