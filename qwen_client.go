package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

type QwenClient struct {
	client *http.Client
	logger *Logger
}

func NewQwenClient(logger *Logger) *QwenClient {
	client := &http.Client{
		Timeout: 30 * time.Second,
	}
	
	return &QwenClient{
		client: client,
		logger: logger,
	}
}

func (qc *QwenClient) ProcessImage(imageNum int) error {
	// 构建图片URL
	imageURL := fmt.Sprintf("%sca%04d.jpg", ImageBaseURL, imageNum)
	
	// 构建请求体 - 使用修改后的参数
	request := &QwenRequest{
		Input: Input{
			Messages: []Message{
				{
					Content: []Content{
						{
							Text: "精准识别考题,严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"*\":\"****\"}}",
						},
					},
					Role: "system",
				},
				{
					Content: []Content{
						{
							Image: imageURL,
						},
						{
							Text: "完整且精准的识别",
						},
					},
					Role: "user",
				},
			},
		},
		Model: "qwen-vl-plus",
		Parameters: Parameters{
			PresencePenalty:   1,
			RepetitionPenalty: 1,
			ResponseFormat: ResponseFormat{
				Type: "json_object",
			},
			Temperature: 0.2,
			TopK:        1,
			TopP:        0.01,
		},
	}

	// 记录请求数据
	if err := qc.logger.LogRequest(imageNum, request); err != nil {
		return fmt.Errorf("failed to log request for image %d: %v", imageNum, err)
	}

	// 序列化请求体
	requestBody, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("failed to marshal request for image %d: %v", imageNum, err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", QwenAPIURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return fmt.Errorf("failed to create request for image %d: %v", imageNum, err)
	}

	// 设置请求头
	req.Header.Set("Authorization", "Bearer "+APIKey)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := qc.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request for image %d: %v", imageNum, err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("API request failed for image %d: status %d, body: %s", 
			imageNum, resp.StatusCode, string(body))
	}

	// 读取响应体
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body for image %d: %v", imageNum, err)
	}

	// 解析响应
	var response QwenResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return fmt.Errorf("failed to parse response for image %d: %v", imageNum, err)
	}

	// 记录响应数据
	if err := qc.logger.LogResponse(imageNum, &response); err != nil {
		return fmt.Errorf("failed to log response for image %d: %v", imageNum, err)
	}

	// 提取并记录结果
	if len(response.Output.Choices) > 0 && len(response.Output.Choices[0].Message.Content) > 0 {
		result := response.Output.Choices[0].Message.Content[0].Text
		
		// 记录完整的JSON结果
		if err := qc.logger.LogResult(imageNum, result); err != nil {
			return fmt.Errorf("failed to log result for image %d: %v", imageNum, err)
		}
		
		// 解析JSON并提取text字段
		var parsedResult map[string]interface{}
		if err := json.Unmarshal([]byte(result), &parsedResult); err == nil {
			if textValue, exists := parsedResult["text"]; exists {
				if textStr, ok := textValue.(string); ok {
					// 记录纯text内容
					if err := qc.logger.LogTextOnly(imageNum, textStr); err != nil {
						return fmt.Errorf("failed to log text only for image %d: %v", imageNum, err)
					}
				}
			}
		}
		
		fmt.Printf("Processed image %04d successfully\n", imageNum)
	} else {
		fmt.Printf("No content found in response for image %04d\n", imageNum)
	}

	return nil
}
